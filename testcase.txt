Failed tests
Test 1: Test 1
Test 2: Test 2
Test 3: Test 3
Test 4: Test 4
Test 5: Test 5
Test 1: Test 1
Incorrect program output
--- Input ---

--- Program output ---
Move: Af Am -> R
Move: Am -> L
Move: Bf Cf -> R
Move: Af -> L
Move: Bm Cm -> R
Move: Bf Bm -> L
Move: Am Bm -> R
Move: Cf -> L
Move: Af Bf -> R
Move: Af -> L
Move: Af Cf -> R

--- Expected output (text)---

Move: Am Af -> R
Move: Am -> L
Move: Bm Bf -> R
Move: Bm -> L
Move: Cm Cf -> R
Move: Am -> L
Move: Am Af -> R
Move: Am -> L
Move: Am Bm -> R
Move: Af -> L
Move: Af Bf -> R
Move: Bm -> L
Move: Bm Am -> R

Test 2: Test 2
Incorrect program output
--- Input ---

--- Program output ---
Move: Af Am -> R
Move: Am -> L
Move: Bf Cf -> R
Move: Af -> L
Move: Bm Cm -> R
Move: Bf Bm -> L
Move: Am Bm -> R
Move: Cf -> L
Move: Af Bf -> R
Move: Af -> L
Move: Af Cf -> R

--- Expected output (text)---

Move: Am Af -> R
Move: Am -> L
Move: Bm Bf -> R
Move: Bm -> L
Move: Cm Cf -> R
Move: Am -> L
Move: Am Af -> R
Move: Am -> L
Move: Am Bm -> R
Move: Af -> L
Move: Af Bf -> R
Move: Bm -> L
Move: Bm Am -> R

Test 3: Test 3
Incorrect program output
--- Input ---

--- Program output ---
Move: Af Am -> R
Move: Am -> L
Move: Bf Cf -> R
Move: Af -> L
Move: Bm Cm -> R
Move: Bf Bm -> L
Move: Am Bm -> R
Move: Cf -> L
Move: Af Bf -> R
Move: Af -> L
Move: Af Cf -> R

--- Expected output (text)---

Move: Am Af -> R
Move: Am -> L
Move: Bm Bf -> R
Move: Bm -> L
Move: Cm Cf -> R
Move: Am -> L
Move: Am Af -> R
Move: Am -> L
Move: Am Bm -> R
Move: Af -> L
Move: Af Bf -> R
Move: Bm -> L
Move: Bm Am -> R

Test 4: Test 4
Incorrect program output
--- Input ---

--- Program output ---
Move: Af Am -> R
Move: Am -> L
Move: Bf Cf -> R
Move: Af -> L
Move: Bm Cm -> R
Move: Bf Bm -> L
Move: Am Bm -> R
Move: Cf -> L
Move: Af Bf -> R
Move: Af -> L
Move: Af Cf -> R

--- Expected output (text)---

Move: Am Af -> R
Move: Am -> L
Move: Bm Bf -> R
Move: Bm -> L
Move: Cm Cf -> R
Move: Am -> L
Move: Am Af -> R
Move: Am -> L
Move: Am Bm -> R
Move: Af -> L
Move: Af Bf -> R
Move: Bm -> L
Move: Bm Am -> R

Test 5: Test 5
Incorrect program output
--- Input ---

--- Program output ---
Move: Af Am -> R
Move: Am -> L
Move: Bf Cf -> R
Move: Af -> L
Move: Bm Cm -> R
Move: Bf Bm -> L
Move: Am Bm -> R
Move: Cf -> L
Move: Af Bf -> R
Move: Af -> L
Move: Af Cf -> R

--- Expected output (text)---

Move: Am Af -> R
Move: Am -> L
Move: Bm Bf -> R
Move: Bm -> L
Move: Cm Cf -> R
Move: Am -> L
Move: Am Af -> R
Move: Am -> L
Move: Am Bm -> R
Move: Af -> L
Move: Af Bf -> R
Move: Bm -> L
Move: Bm Am -> R

Summary of tests
+------------------------------+
|  5 tests run/ 0 tests passed |
+--------------------