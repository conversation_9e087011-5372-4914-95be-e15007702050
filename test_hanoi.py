#!/usr/bin/env python3
"""
Test suite cho Tháp <PERSON> Nội IDDFS
"""

import subprocess
import sys

def test_case(n, expected_output):
    """Test một case cụ thể"""
    try:
        # Ch<PERSON>y chương trình với input
        result = subprocess.run(
            [sys.executable, "hanoi_simple.py"],
            input=str(n),
            text=True,
            capture_output=True,
            timeout=30
        )
        
        if result.returncode != 0:
            print(f"❌ Test n={n}: Program crashed")
            print(f"Error: {result.stderr}")
            return False
        
        actual_output = result.stdout.strip()
        expected_output = expected_output.strip()
        
        if actual_output == expected_output:
            print(f"✅ Test n={n}: PASSED")
            return True
        else:
            print(f"❌ Test n={n}: FAILED")
            print("Expected:")
            for line in expected_output.split('\n'):
                print(f"  {line}")
            print("Actual:")
            for line in actual_output.split('\n'):
                print(f"  {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ Test n={n}: TIMEOUT")
        return False
    except Exception as e:
        print(f"❌ Test n={n}: ERROR - {e}")
        return False

def run_tests():
    """Chạy tất cả test cases"""
    print("=" * 50)
    print("TESTING HANOI IDDFS SOLUTION")
    print("=" * 50)
    
    test_cases = [
        (1, "Move disk 1 from A to C"),
        (2, """Move disk 1 from A to B
Move disk 2 from A to C
Move disk 1 from B to C"""),
        (3, """Move disk 1 from A to C
Move disk 2 from A to B
Move disk 1 from C to B
Move disk 3 from A to C
Move disk 1 from B to A
Move disk 2 from B to C
Move disk 1 from A to C"""),
        (4, """Move disk 1 from A to B
Move disk 2 from A to C
Move disk 1 from B to C
Move disk 3 from A to B
Move disk 1 from C to A
Move disk 2 from C to B
Move disk 1 from A to B
Move disk 4 from A to C
Move disk 1 from B to C
Move disk 2 from B to A
Move disk 1 from C to A
Move disk 3 from B to C
Move disk 1 from A to B
Move disk 2 from A to C
Move disk 1 from B to C""")
    ]
    
    passed = 0
    total = len(test_cases)
    
    for n, expected in test_cases:
        if test_case(n, expected):
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Solution is ready for submission!")
        return True
    else:
        print("❌ Some tests failed.")
        return False

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
