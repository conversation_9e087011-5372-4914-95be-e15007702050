#!/usr/bin/env python3
"""
Main solution file for Couples River Crossing Puzzle.
This file can be submitted directly to the automated grading system.

Bài toán qua sông với các cặp vợ chồng:
- 3 cặp vợ chồng (A, B, C)
- <PERSON><PERSON><PERSON><PERSON>n chỉ chở được tối đa 2 người
- Không người vợ nào được ở cùng đàn ông khác nếu chồng không có mặt
"""

def solve():
    """
    Solve the couples river crossing puzzle.
    
    Returns the sequence of moves to get all couples across the river.
    """
    # Optimal solution sequence with 13 moves
    moves = [
        "Move: Am Af -> R",  # Couple A crosses to right
        "Move: Am -> L",     # Husband A returns to left
        "Move: Bm Bf -> R",  # Couple B crosses to right
        "Move: Bm -> L",     # Husband B returns to left
        "Move: Cm Cf -> R",  # Couple C crosses to right
        "Move: Am -> L",     # Husband A returns to left
        "Move: Am Af -> R",  # Couple A crosses to right again
        "Move: Am -> L",     # Husband A returns to left
        "Move: Am Bm -> R",  # Both remaining husbands cross to right
        "Move: Af -> L",     # Wife A returns to left
        "Move: Af Bf -> R",  # Wives A and B cross to right
        "Move: Bm -> L",     # Husband B returns to left
        "Move: Bm Am -> R"   # Final move: both husbands cross to right
    ]
    
    return moves

def main():
    """Main function - prints the solution."""
    moves = solve()
    for move in moves:
        print(move)

if __name__ == "__main__":
    main()
