#!/usr/bin/env python3
"""
Debug the couples crossing solution by tracing through step by step.
"""

def trace_solution():
    """Trace through the expected solution step by step."""
    
    # Expected moves from test case
    expected_moves = [
        "Move: Am Af -> R",
        "Move: Am -> L", 
        "Move: Bm Bf -> R",
        "Move: Bm -> L",
        "Move: Cm Cf -> R",
        "Move: Am -> L",  # This is the problematic move
        "Move: Am Af -> R",
        "Move: Am -> L",
        "Move: Am Bm -> R",
        "Move: Af -> L",
        "Move: Af Bf -> R",
        "Move: Bm -> L",
        "Move: Bm Am -> R"
    ]
    
    # Initial state
    left_side = {'Am', 'Af', 'Bm', 'Bf', 'Cm', 'Cf'}
    right_side = set()
    
    print("Initial state:")
    print(f"  Left: {sorted(left_side)}")
    print(f"  Right: {sorted(right_side)}")
    print()
    
    for i, move in enumerate(expected_moves):
        print(f"Move {i+1}: {move}")
        
        # Parse move
        parts = move.split()
        direction = parts[-1]
        people_part = ' '.join(parts[1:-2])
        people = people_part.split()
        
        print(f"  People moving: {people}")
        print(f"  Direction: {direction}")
        
        # Check if people are on correct side before move
        if direction == 'R':
            for person in people:
                if person not in left_side:
                    print(f"  ❌ ERROR: {person} not on left side!")
                    print(f"    Left: {sorted(left_side)}")
                    print(f"    Right: {sorted(right_side)}")
                    return
        else:  # direction == 'L'
            for person in people:
                if person not in right_side:
                    print(f"  ❌ ERROR: {person} not on right side!")
                    print(f"    Left: {sorted(left_side)}")
                    print(f"    Right: {sorted(right_side)}")
                    return
        
        # Execute move
        if direction == 'R':
            for person in people:
                left_side.remove(person)
                right_side.add(person)
        else:
            for person in people:
                right_side.remove(person)
                left_side.add(person)
        
        print(f"  After move:")
        print(f"    Left: {sorted(left_side)}")
        print(f"    Right: {sorted(right_side)}")
        print()

if __name__ == "__main__":
    trace_solution()
