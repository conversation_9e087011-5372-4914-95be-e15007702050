#!/usr/bin/env python3
"""
Th<PERSON><PERSON> sử dụng IDDFS - Version đơn giản
"""

class HanoiState:
    def __init__(self, towers):
        self.towers = [list(tower) for tower in towers]
    
    def __eq__(self, other):
        return self.towers == other.towers
    
    def __hash__(self):
        return hash(tuple(tuple(tower) for tower in self.towers))
    
    def is_goal(self, n):
        return (len(self.towers[0]) == 0 and 
                len(self.towers[1]) == 0 and 
                len(self.towers[2]) == n)
    
    def get_moves(self):
        moves = []
        for from_col in range(3):
            if not self.towers[from_col]:
                continue
            disk = self.towers[from_col][-1]
            for to_col in range(3):
                if from_col == to_col:
                    continue
                if (not self.towers[to_col] or self.towers[to_col][-1] > disk):
                    new_towers = [list(tower) for tower in self.towers]
                    new_towers[from_col].pop()
                    new_towers[to_col].append(disk)
                    new_state = HanoiState(new_towers)
                    move_desc = f"Move disk {disk} from {chr(65+from_col)} to {chr(65+to_col)}"
                    moves.append((new_state, move_desc))
        return moves

def dls(state, goal_n, depth, path, visited):
    if state.is_goal(goal_n):
        return True, path
    if depth <= 0:
        return False, []
    if state in visited:
        return False, []
    
    visited.add(state)
    for next_state, move in state.get_moves():
        success, result = dls(next_state, goal_n, depth - 1, path + [move], visited)
        if success:
            return True, result
    visited.remove(state)
    return False, []

def iddfs(n):
    initial = HanoiState([list(range(n, 0, -1)), [], []])
    max_depth = (2 ** n) - 1
    
    for depth in range(1, max_depth + 1):
        visited = set()
        success, path = dls(initial, n, depth, [], visited)
        if success:
            return path
    return []

# Main
n = int(input())
moves = iddfs(n)
for move in moves:
    print(move)
