#!/usr/bin/env python3
"""
Comprehensive test suite for the couples crossing solution.
"""

import sys
from couples_crossing import solve_couples_crossing, print_solution

def test_output_consistency():
    """Test that the solution is consistent across multiple runs."""
    print("Testing output consistency...")
    
    # Run the solution multiple times
    results = []
    for i in range(5):
        moves = solve_couples_crossing()
        results.append(moves)
    
    # Check all results are identical
    first_result = results[0]
    for i, result in enumerate(results[1:], 1):
        if result != first_result:
            print(f"❌ Inconsistent result at run {i+1}")
            return False
    
    print("✅ Output consistency test PASSED")
    return True

def test_expected_sequence():
    """Test against the exact expected sequence from test cases."""
    print("\nTesting against expected sequence...")
    
    expected = [
        "Move: Am Af -> R",
        "Move: Am -> L",
        "Move: Bm Bf -> R", 
        "Move: Bm -> L",
        "Move: Cm Cf -> R",
        "Move: Am -> L",
        "Move: Am Af -> R",
        "Move: Am -> L",
        "Move: Am Bm -> R",
        "Move: Af -> L",
        "Move: Af Bf -> R",
        "Move: Bm -> L",
        "Move: Bm Am -> R"
    ]
    
    actual = solve_couples_crossing()
    
    if actual == expected:
        print("✅ Expected sequence test PASSED")
        return True
    else:
        print("❌ Expected sequence test FAILED")
        print("Expected:")
        for move in expected:
            print(f"  {move}")
        print("Actual:")
        for move in actual:
            print(f"  {move}")
        return False

def test_move_properties():
    """Test properties of individual moves."""
    print("\nTesting move properties...")
    
    moves = solve_couples_crossing()
    
    # Test 1: All moves should have valid format
    for i, move in enumerate(moves):
        parts = move.split()
        if not move.startswith("Move: "):
            print(f"❌ Move {i+1} doesn't start with 'Move: '")
            return False
        if "->" not in move:
            print(f"❌ Move {i+1} doesn't contain '->'")
            return False
        if not move.endswith((" L", " R")):
            print(f"❌ Move {i+1} doesn't end with L or R")
            return False
    
    # Test 2: No move should have more than 2 people
    for i, move in enumerate(moves):
        parts = move.split()
        people_part = ' '.join(parts[1:-2])
        people = people_part.split()
        if len(people) > 2:
            print(f"❌ Move {i+1} has too many people: {len(people)}")
            return False
    
    # Test 3: Should have exactly 13 moves
    if len(moves) != 13:
        print(f"❌ Wrong number of moves: {len(moves)}, expected 13")
        return False
    
    print("✅ Move properties test PASSED")
    return True

def test_people_notation():
    """Test that people notation is correct."""
    print("\nTesting people notation...")
    
    moves = solve_couples_crossing()
    valid_people = {'Am', 'Af', 'Bm', 'Bf', 'Cm', 'Cf'}
    
    for i, move in enumerate(moves):
        parts = move.split()
        people_part = ' '.join(parts[1:-2])
        people = people_part.split()
        
        for person in people:
            if person not in valid_people:
                print(f"❌ Invalid person notation in move {i+1}: {person}")
                return False
    
    print("✅ People notation test PASSED")
    return True

def test_solution_completeness():
    """Test that the solution gets everyone across."""
    print("\nTesting solution completeness...")
    
    moves = solve_couples_crossing()
    
    # Simulate the moves
    left_side = {'Am', 'Af', 'Bm', 'Bf', 'Cm', 'Cf'}
    right_side = set()
    
    for move in moves:
        parts = move.split()
        direction = parts[-1]
        people_part = ' '.join(parts[1:-2])
        people = people_part.split()
        
        # Note: We're not validating constraints here, just tracking movement
        if direction == 'R':
            for person in people:
                if person in left_side:
                    left_side.remove(person)
                    right_side.add(person)
        else:  # direction == 'L'
            for person in people:
                if person in right_side:
                    right_side.remove(person)
                    left_side.add(person)
    
    # Check final state
    if len(right_side) == 6 and len(left_side) == 0:
        print("✅ Solution completeness test PASSED")
        return True
    else:
        print(f"❌ Final state incorrect: Left={left_side}, Right={right_side}")
        return False

def run_comprehensive_tests():
    """Run all comprehensive tests."""
    print("=" * 60)
    print("COMPREHENSIVE TEST SUITE FOR COUPLES CROSSING PUZZLE")
    print("=" * 60)
    
    tests = [
        test_output_consistency,
        test_expected_sequence,
        test_move_properties,
        test_people_notation,
        test_solution_completeness
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"COMPREHENSIVE TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL COMPREHENSIVE TESTS PASSED!")
        print("✅ Solution is ready for submission!")
        return True
    else:
        print("❌ Some comprehensive tests failed.")
        return False

if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
