#!/usr/bin/env python3
"""
Test script for the Couples River Crossing Puzzle solution.
"""

import sys
from couples_crossing import solve_couples_crossing, validate_solution

def test_output_format():
    """Test that the output format matches expected format."""
    expected_moves = [
        "Move: Am Af -> R",
        "Move: Am -> L", 
        "Move: Bm Bf -> R",
        "Move: Bm -> L",
        "Move: Cm Cf -> R",
        "Move: Am -> L",
        "Move: Am Af -> R",
        "Move: Am -> L",
        "Move: Am Bm -> R",
        "Move: Af -> L",
        "Move: Af Bf -> R",
        "Move: Bm -> L",
        "Move: Bm Am -> R"
    ]
    
    actual_moves = solve_couples_crossing()
    
    print("Testing output format...")
    if actual_moves == expected_moves:
        print("✅ Output format test PASSED")
        return True
    else:
        print("❌ Output format test FAILED")
        print("Expected:")
        for move in expected_moves:
            print(f"  {move}")
        print("Actual:")
        for move in actual_moves:
            print(f"  {move}")
        return False

def test_solution_validity():
    """Test that the solution follows all constraints."""
    print("\nTesting solution validity...")
    if validate_solution():
        print("✅ Solution validity test PASSED")
        return True
    else:
        print("❌ Solution validity test FAILED")
        return False

def test_move_count():
    """Test that the solution uses the optimal number of moves."""
    moves = solve_couples_crossing()
    expected_count = 13
    actual_count = len(moves)
    
    print(f"\nTesting move count...")
    print(f"Expected moves: {expected_count}")
    print(f"Actual moves: {actual_count}")
    
    if actual_count == expected_count:
        print("✅ Move count test PASSED")
        return True
    else:
        print("❌ Move count test FAILED")
        return False

def test_constraint_checking():
    """Test basic move validation (simplified)."""
    print("\nTesting basic move validation...")

    # For now, just test that moves are valid in terms of boat capacity and people movement
    moves = solve_couples_crossing()

    if len(moves) == 13:
        print("✅ Correct number of moves")
    else:
        print(f"❌ Wrong number of moves: {len(moves)}")
        return False

    # Test that each move has valid format
    for i, move in enumerate(moves):
        parts = move.split()
        if len(parts) < 3 or parts[-2] != '->' or parts[-1] not in ['L', 'R']:
            print(f"❌ Invalid move format at move {i+1}: {move}")
            return False

        people_part = ' '.join(parts[1:-2])
        people = people_part.split()

        if len(people) > 2:
            print(f"❌ Too many people in move {i+1}: {move}")
            return False

    print("✅ Basic move validation test PASSED")
    return True

def run_all_tests():
    """Run all tests and return overall result."""
    print("=" * 50)
    print("COUPLES RIVER CROSSING PUZZLE - TEST SUITE")
    print("=" * 50)
    
    tests = [
        test_output_format,
        test_solution_validity,
        test_move_count,
        test_constraint_checking
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Solution is correct.")
        return True
    else:
        print("❌ Some tests failed. Please check the solution.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
