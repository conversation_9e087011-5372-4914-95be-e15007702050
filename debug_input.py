#!/usr/bin/env python3
"""
Debug script để kiểm tra input handling
"""

import sys

def debug_input():
    """Debug input reading"""
    try:
        print("Reading input...", file=sys.stderr)
        line = input()
        print(f"Raw input: '{line}'", file=sys.stderr)
        print(f"Stripped: '{line.strip()}'", file=sys.stderr)
        print(f"Length: {len(line.strip())}", file=sys.stderr)
        
        n = int(line.strip())
        print(f"Parsed n: {n}", file=sys.stderr)
        
        # Test the actual function
        from hanoi_iddfs import solve_hanoi
        moves = solve_hanoi(n)
        
        for move in moves:
            print(move)
            
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        print(f"Error type: {type(e)}", file=sys.stderr)

if __name__ == "__main__":
    debug_input()
