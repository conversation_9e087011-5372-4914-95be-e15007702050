#!/usr/bin/env python3
"""
Thá<PERSON> sử dụng Iterative Deepening Depth-First Search (IDDFS)

Bài toán: Di chuyển n đĩa từ cột A sang cột C, sử dụng cột B làm trung gian.
Quy tắc:
- Chỉ được di chuyển một đĩa mỗi lần
- Đĩa lớn không được đặt lên đĩa nhỏ
- Chỉ được di chuyển đĩa ở trên cùng của mỗi cột

Đầu vào: n (1 ≤ n ≤ 4) - số lượng đĩa
Đầu ra: <PERSON><PERSON><PERSON> bước di chuyển dạng "Move disk X from A to C"
"""

class HanoiState:
    """Trạng thái của bài toán Tháp Hà Nội"""
    
    def __init__(self, towers):
        """
        towers: list of 3 lists, mỗi list chứa các đĩa trên cột đó
        Đĩa được biểu diễn bằng số, đĩa nhỏ có số nhỏ hơn
        """
        self.towers = [list(tower) for tower in towers]  # Deep copy
    
    def __eq__(self, other):
        """Kiểm tra hai trạng thái có bằng nhau không"""
        return self.towers == other.towers
    
    def __hash__(self):
        """Hash để có thể dùng trong set"""
        return hash(tuple(tuple(tower) for tower in self.towers))
    
    def __str__(self):
        """String representation cho debug"""
        return f"A:{self.towers[0]} B:{self.towers[1]} C:{self.towers[2]}"
    
    def is_goal(self, n):
        """Kiểm tra có phải trạng thái đích không"""
        return (len(self.towers[0]) == 0 and 
                len(self.towers[1]) == 0 and 
                len(self.towers[2]) == n)
    
    def get_valid_moves(self):
        """Lấy tất cả các nước đi hợp lệ từ trạng thái hiện tại"""
        moves = []
        
        for from_col in range(3):
            if not self.towers[from_col]:  # Cột rỗng
                continue
                
            disk = self.towers[from_col][-1]  # Đĩa trên cùng
            
            for to_col in range(3):
                if from_col == to_col:  # Không di chuyển cùng cột
                    continue
                
                # Kiểm tra có thể đặt đĩa lên cột đích không
                if (not self.towers[to_col] or  # Cột đích rỗng
                    self.towers[to_col][-1] > disk):  # Đĩa trên cột đích lớn hơn
                    
                    # Tạo trạng thái mới
                    new_towers = [list(tower) for tower in self.towers]
                    new_towers[from_col].pop()  # Lấy đĩa ra
                    new_towers[to_col].append(disk)  # Đặt đĩa vào
                    
                    new_state = HanoiState(new_towers)
                    move_desc = f"Move disk {disk} from {chr(65+from_col)} to {chr(65+to_col)}"
                    moves.append((new_state, move_desc))
        
        return moves

def depth_limited_search(start_state, goal_n, depth_limit, path, visited):
    """
    Depth-Limited Search
    
    Args:
        start_state: Trạng thái bắt đầu
        goal_n: Số đĩa (để kiểm tra đích)
        depth_limit: Giới hạn độ sâu
        path: Đường đi hiện tại (list các move descriptions)
        visited: Set các trạng thái đã thăm
    
    Returns:
        (success, path) - success: bool, path: list of moves
    """
    if start_state.is_goal(goal_n):
        return True, path
    
    if depth_limit <= 0:
        return False, []
    
    if start_state in visited:
        return False, []
    
    visited.add(start_state)
    
    # Thử tất cả các nước đi có thể
    for next_state, move_desc in start_state.get_valid_moves():
        success, result_path = depth_limited_search(
            next_state, goal_n, depth_limit - 1, 
            path + [move_desc], visited
        )
        if success:
            return True, result_path
    
    visited.remove(start_state)  # Backtrack
    return False, []

def iterative_deepening_search(n):
    """
    Iterative Deepening Depth-First Search cho Tháp Hà Nội
    
    Args:
        n: Số lượng đĩa
    
    Returns:
        List các bước di chuyển
    """
    # Trạng thái ban đầu: tất cả đĩa ở cột A
    initial_towers = [list(range(n, 0, -1)), [], []]  # [n, n-1, ..., 1], [], []
    start_state = HanoiState(initial_towers)
    
    # Số bước tối thiểu cho n đĩa là 2^n - 1
    max_depth = (2 ** n) - 1
    
    # Thử từng độ sâu từ 1 đến max_depth
    for depth in range(1, max_depth + 1):
        visited = set()
        success, path = depth_limited_search(start_state, n, depth, [], visited)
        
        if success:
            return path
    
    return []  # Không tìm thấy solution (không bao giờ xảy ra với Tháp Hà Nội)

def solve_hanoi(n):
    """
    Giải bài toán Tháp Hà Nội với n đĩa
    
    Args:
        n: Số lượng đĩa (1 ≤ n ≤ 4)
    
    Returns:
        List các bước di chuyển
    """
    if n < 1 or n > 4:
        raise ValueError("n phải trong khoảng 1 ≤ n ≤ 4")
    
    return iterative_deepening_search(n)

def main():
    """Hàm main - đọc input và in output"""
    try:
        n = int(input().strip())
        
        if n < 1 or n > 4:
            print("Error: n phải trong khoảng 1 ≤ n ≤ 4")
            return
        
        moves = solve_hanoi(n)
        
        for move in moves:
            print(move)
            
    except ValueError:
        print("Error: Vui lòng nhập một số nguyên hợp lệ")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
